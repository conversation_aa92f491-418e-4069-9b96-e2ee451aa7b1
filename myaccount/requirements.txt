Markdown==3.0.1
Django==2.2
freezegun==0.3.9
python-dateutil==2.8.*
pytz==2020.1
six==1.11.0
djangorestframework==3.9.2
django-filter==2.0.0
drf-yasg==1.14.0
python-keycloak==2.8.0
model-mommy==1.6.0
mock==2.0.0
psycopg2-binary==2.9.1
coverage==4.5.1
django-cors-headers==2.4.0
xlrd==1.2.0
boto3==1.23.10
Unidecode==1.0.23
htmlmin==0.1.12
twilio==9.4.6
celery==4.4.7
redis==3.2.1
newrelic==4.20.1.121
django-nose==1.4.6
pytest-cov==2.8.1
pytest-django==3.9.0
django-injector==0.1.1
messagebird==1.5.0
slackclient==1.3.1
gunicorn==20.1.0
gevent==21.8.0
greenlet==1.1.2
zope.event==4.5.0
zope.interface==5.4.0
pandas==2.2.0
pycodestyle==2.8.0
pylint-django==2.5.3
pylint==2.14.0
pylint_report==0.1.8
pre_commit==2.15.0
phonenumbers==8.12.42
Jinja2==3.0.0
APScheduler==3.9.1
singletons==0.2.5
pillow==9.0.1
discord-webhook==0.17.0
psutil==5.9.1
elastic-apm==6.13.2
pycryptodome==3.17
confluent_kafka==2.1.1
rules==3.3
validate-docbr==1.10.0
grpcio-tools==1.71.0
core @ git+https://<EMAIL>/Keeps-Learn/keeps-python-core.git@0.2.0